# 手机名称显示问题修复说明

## 问题描述
在生成每日工作数据时，分配汇总中缺少手机名称，但每日工作数据里面有该手机名称的数据。

## 问题原因
在 `core/excel_generator.py` 文件中，记录手机名称的逻辑存在问题：

1. **原始代码问题**：
   ```python
   # 记录手机名称
   if 'phone_name' in info:
       staff_data[staff_name][base_operation_code]['phone_names'].add(info['phone_name'])
   ```
   
   这个逻辑只检查了 `phone_name` 键是否存在，但没有检查其值是否为空。当分配引擎无法获取手机名称时，会设置 `'phone_name': ""`（空字符串），这个空字符串会被添加到 `phone_names` 集合中。

2. **后续处理问题**：
   在显示手机名称汇总时，虽然有过滤逻辑，但没有完全过滤掉所有无效值。

## 修复方案

### 1. 修复手机名称记录逻辑
**文件**: `core/excel_generator.py` 第68-70行

**修改前**:
```python
# 记录手机名称
if 'phone_name' in info:
    staff_data[staff_name][base_operation_code]['phone_names'].add(info['phone_name'])
```

**修改后**:
```python
# 记录手机名称（只记录非空的手机名称）
if 'phone_name' in info and info['phone_name'] and info['phone_name'].strip():
    staff_data[staff_name][base_operation_code]['phone_names'].add(info['phone_name'].strip())
```

### 2. 完善手机名称过滤逻辑
**文件**: `core/excel_generator.py` 多个位置

**修改内容**:
- 第614行：添加对"未分组"的过滤
- 第619行：完善多手机名称分割时的过滤
- 第1589行：在K列宽度计算时添加过滤
- 第1594行：完善K列宽度计算时的多手机名称过滤

**修改示例**:
```python
# 修改前
if phone_name and phone_name not in ('未知手机', ''):

# 修改后  
if phone_name and phone_name not in ('未知手机', '', '未分组'):
```

## 修复效果

1. **分配汇总表**：现在只会显示有效的手机名称，空字符串和无效值不会出现
2. **手机名称汇总列（K列）**：只显示真正有效的手机名称
3. **每日工作数据**：保持原有功能，正确显示手机名称对应的数据

## 测试验证

创建了测试脚本 `test_phone_name_fix.py` 验证修复逻辑：
- ✅ 空字符串被正确过滤
- ✅ "未分组"和"未知手机"被正确过滤
- ✅ 多手机名称（逗号分隔）被正确处理
- ✅ 有效手机名称被正确保留

## 注意事项

1. 此修复不会影响每日工作数据的生成，只是优化了分配汇总的显示
2. 如果某个运营编码确实没有对应的手机名称，在分配汇总中会显示为"未分组"
3. 修复后的逻辑更加严格，只有真正有效的手机名称才会被记录和显示

## 相关文件

- `core/excel_generator.py` - 主要修复文件
- `core/allocation_engine.py` - 手机名称获取逻辑（无需修改）
- `test_phone_name_fix.py` - 测试验证脚本
