#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手机名称修复的脚本
"""

def test_phone_name_filtering():
    """测试手机名称过滤逻辑"""
    
    # 模拟分配数据
    test_data = [
        {'phone_name': '手机1', 'operation_code': 'OP001'},
        {'phone_name': '', 'operation_code': 'OP002'},  # 空字符串
        {'phone_name': '手机2', 'operation_code': 'OP003'},
        {'phone_name': '未分组', 'operation_code': 'OP004'},  # 未分组
        {'phone_name': '未知手机', 'operation_code': 'OP005'},  # 未知手机
        {'phone_name': '手机3, 手机4', 'operation_code': 'OP006'},  # 多个手机
        {'phone_name': '   ', 'operation_code': 'OP007'},  # 空白字符
    ]
    
    # 测试手机名称收集逻辑（模拟修复后的逻辑）
    phone_names = set()
    for info in test_data:
        if 'phone_name' in info and info['phone_name'] and info['phone_name'].strip():
            # 过滤掉无效值
            phone_name = info['phone_name'].strip()
            if phone_name not in ('未知手机', '未分组'):
                phone_names.add(phone_name)
    
    print("收集到的手机名称:")
    for phone in sorted(phone_names):
        print(f"  - {phone}")
    
    # 测试汇总显示逻辑
    all_phone_names = set()
    for data in test_data:
        phone_name = data.get('phone_name', '')
        if phone_name and phone_name.strip() and phone_name not in ('未知手机', '', '未分组'):
            # 处理可能的多个手机名称（用逗号分隔）
            if ',' in phone_name:
                phone_names_list = [name.strip() for name in phone_name.split(',')]
                # 过滤掉空字符串或无效值
                phone_names_list = [name for name in phone_names_list if name and name.strip() and name not in ('未知手机', '', '未分组')]
                all_phone_names.update(phone_names_list)
            else:
                all_phone_names.add(phone_name.strip())
    
    print("\n汇总显示的手机名称:")
    for phone in sorted(all_phone_names):
        print(f"  - {phone}")
    
    # 验证结果
    expected_phones = {'手机1', '手机2', '手机3', '手机4'}
    if all_phone_names == expected_phones:
        print("\n✅ 测试通过：手机名称过滤逻辑正确")
        return True
    else:
        print(f"\n❌ 测试失败：期望 {expected_phones}，实际 {all_phone_names}")
        return False

def test_summary_data_logic():
    """测试汇总数据逻辑"""
    
    # 模拟合并后的数据
    merged_info = {
        'phone_names': {'手机1', '手机2'},  # 有效手机名称
        'total_rows': 100
    }
    
    # 测试显示逻辑
    phone_names = list(merged_info['phone_names']) if merged_info['phone_names'] else ['未分组']
    phone_display = ', '.join(sorted(phone_names)) if phone_names != ['未分组'] else '未分组'
    
    print(f"\n汇总显示结果: {phone_display}")
    
    # 测试空集合的情况
    empty_merged_info = {
        'phone_names': set(),  # 空集合
        'total_rows': 50
    }
    
    phone_names_empty = list(empty_merged_info['phone_names']) if empty_merged_info['phone_names'] else ['未分组']
    phone_display_empty = ', '.join(sorted(phone_names_empty)) if phone_names_empty != ['未分组'] else '未分组'
    
    print(f"空集合显示结果: {phone_display_empty}")
    
    return phone_display == '手机1, 手机2' and phone_display_empty == '未分组'

if __name__ == "__main__":
    print("开始测试手机名称修复逻辑...")
    
    test1_result = test_phone_name_filtering()
    test2_result = test_summary_data_logic()
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复逻辑正确。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
