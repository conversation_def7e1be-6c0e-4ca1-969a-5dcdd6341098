#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试每日工作表手机名称汇总功能
"""

import pandas as pd
from openpyxl import Workbook
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_phone_summary_logic():
    """测试手机名称汇总逻辑"""
    
    # 模拟客服分配到的手机
    staff_phones = {'手机1', '手机3', '手机5'}
    
    # 模拟每日工作数据
    daily_work_data = {
        '运营编码': ['OP001', 'OP002', 'OP003', 'OP004', 'OP005'],
        '手机名称': ['手机1', '手机2', '手机3', '手机1', '手机5'],
        '订单编号': ['12345', '12346', '12347', '12348', '12349'],
        '评价内容': ['好评1', '好评2', '好评3', '好评4', '好评5']
    }
    
    staff_daily_df = pd.DataFrame(daily_work_data)
    
    print("模拟数据:")
    print(f"客服分配到的手机: {sorted(staff_phones)}")
    print(f"每日工作数据中的手机: {staff_daily_df['手机名称'].unique().tolist()}")
    
    # 测试筛选逻辑
    # 在实际代码中，这部分逻辑会筛选出只包含该客服手机的数据
    filtered_data = staff_daily_df[staff_daily_df['手机名称'].isin(staff_phones)]
    
    print(f"\n筛选后的数据:")
    print(f"数据行数: {len(filtered_data)}")
    print(f"包含的手机: {filtered_data['手机名称'].unique().tolist()}")
    
    # 验证汇总列应该显示的手机
    summary_phones = sorted(list(staff_phones))
    print(f"\n汇总列应该显示的手机: {summary_phones}")
    
    # 验证筛选结果：应该包含所有分配给该客服的手机数据
    expected_phones = staff_phones
    actual_phones = set(filtered_data['手机名称'].unique())

    success = actual_phones == expected_phones
    print(f"验证结果: {'✅ 通过' if success else '❌ 失败'}")
    if not success:
        print(f"  期望手机: {expected_phones}")
        print(f"  实际手机: {actual_phones}")

    return success

def test_excel_generation():
    """测试Excel生成功能"""
    try:
        # 创建测试工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "测试每日工作数据"
        
        # 添加测试数据
        headers = ['运营编码', '手机名称', '订单编号', '评价内容']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        test_data = [
            ['OP001', '手机1', '12345', '好评1'],
            ['OP003', '手机3', '12347', '好评3'],
            ['OP005', '手机5', '12349', '好评5']
        ]
        
        for row, data in enumerate(test_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)
        
        # 模拟添加手机汇总列
        staff_phones = {'手机1', '手机3', '手机5'}
        last_col = len(headers) + 2  # +2 留一列空隙
        
        # 添加标题
        ws.cell(row=1, column=last_col, value='分配手机汇总')
        
        # 添加手机名称
        sorted_phones = sorted(list(staff_phones))
        for row_idx, phone_name in enumerate(sorted_phones, 2):
            ws.cell(row=row_idx, column=last_col, value=phone_name)
        
        # 保存测试文件
        test_file = Path("test_daily_work_output.xlsx")
        wb.save(test_file)
        
        print(f"\n✅ 测试Excel文件已生成: {test_file}")
        print(f"包含 {len(sorted_phones)} 个分配手机的汇总列")
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
            
        return True
        
    except Exception as e:
        print(f"❌ Excel生成测试失败: {e}")
        return False

def test_column_positioning():
    """测试列位置计算"""
    
    # 模拟不同的数据列数
    test_cases = [
        {'columns': 5, 'expected_pos': 7},   # 5列数据 + 2空隙 = 第7列
        {'columns': 8, 'expected_pos': 10},  # 8列数据 + 2空隙 = 第10列
        {'columns': 3, 'expected_pos': 5},   # 3列数据 + 2空隙 = 第5列
    ]
    
    print("\n测试列位置计算:")
    all_passed = True
    
    for case in test_cases:
        calculated_pos = case['columns'] + 2
        expected_pos = case['expected_pos']
        passed = calculated_pos == expected_pos
        all_passed = all_passed and passed
        
        status = "✅" if passed else "❌"
        print(f"{status} {case['columns']}列数据 -> 汇总列位置: {calculated_pos} (期望: {expected_pos})")
    
    return all_passed

if __name__ == "__main__":
    print("开始测试每日工作表手机名称汇总功能...")
    
    test1_result = test_phone_summary_logic()
    test2_result = test_excel_generation()
    test3_result = test_column_positioning()
    
    print(f"\n测试结果:")
    print(f"手机汇总逻辑: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"Excel生成功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"列位置计算: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 所有测试通过！新功能可以正常工作。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
